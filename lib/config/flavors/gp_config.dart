import 'package:gp_h5/config/flavors/app_config.dart';


void setupGPConfig() {
  AppConfig(
    appName: 'GP Stock',
    siteId: '1',
    baseUrl: 'https://h5.gpnow.xyz',
    marketWsUrl: 'wss://h5.gpnow.xyz/ws',
    inviteLinkUrl: 'https://h5.gpnow.xyz/#/?inviteCode=',
    
    electron: ElectronConfig(
      appId: 'com.gpstock.app',
      productName: 'GP Stock',
      icon: IconConfig(
        window: 'brands/gpstock/assets/icon.png',
        tray: 'brands/gpstock/assets/tray.png',
        build: IconBuildConfig(
          mac: 'brands/gpstock/assets/icon.icns',
          win: 'brands/gpstock/assets/icon.ico',
          linux: 'brands/gpstock/assets/icon.png',
        ),
      ),
      window: WindowConfig(
        minWidth: 1400,
        minHeight: 1200,
        autoHideMenuBar: true,
      ),
      build: BuildConfig(
        directories: BuildDirectoriesConfig(
          output: 'release/\${version}',
        ),
        artifactName: ArtifactNameConfig(
          mac: 'GP-Stock-Mac-\${version}-Installer.\${ext}',
          win: 'GP-Stock-Windows-\${version}-Setup.\${ext}',
        ),
      ),
      macIdentity: 'Parekh Gujjar (V52VA2KKYX)',
    ),
  );
}
