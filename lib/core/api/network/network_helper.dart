import 'package:flutter/material.dart';
import 'dart:async';

class NetworkHelper {
  // Track currently displayed messages
  static final Set<String> _activeMessages = {};

  static Future<void> handleMessage(
    String? message,
    BuildContext context, {
    bool useParentContext = false,
    String? actionButtonText,
    bool? hideHeader,
    String? headerImage,
    VoidCallback? onTap,
    String title = 'Error',
    String? buttonText,
    IconData? icon,
    Color? color,
    Color backgroundColor = Colors.black,
    bool isWarning = false,
    bool isInfinite = false,
    double bottomPadding = 50,
  }) async {
        // Create a unique key for this message
        final messageKey = "$message";
        
        // If this message is already being shown, don't show it again
        if (_activeMessages.contains(messageKey)) {
          return;
        }
        
        // Mark this message as active
        _activeMessages.add(messageKey);
        
        try {
          await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text(title),
              content: Text(message ?? 'Error'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(buttonText ?? 'OK'),
                ),
              ],
            ),
            barrierDismissible: true,
          );
          // If dialog is dismissed by tapping outside (when barrierDismissible is true)
          _activeMessages.remove(messageKey);
        } catch (e) {
          // Ensure we clean up if there's an error
          _activeMessages.remove(messageKey);
        }
        
        showSnackBar(
          message ?? 'Error',
          context,
          backgroundColor: backgroundColor,
          bottomPadding: bottomPadding,
          isWarning: isWarning,
          isInfinite: isInfinite,
          color: color,
          icon: icon,
        );
  }
}

void showSnackBar(
  String message,
  BuildContext context, {
  IconData? icon,
  Color? color,
  Color backgroundColor = Colors.black,
  bool isWarning = false,
  bool isInfinite = false,
  double bottomPadding = 50,
}) {
  ScaffoldMessenger.of(context)
    ..hideCurrentSnackBar()
    ..showSnackBar(
      SnackBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        content: Container(
          constraints: BoxConstraints(minHeight: 40, maxHeight: 50),
          margin: EdgeInsets.only(bottom: bottomPadding - 40),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      message,
                      maxLines: 10,
                      style: TextStyle(
                        fontSize: 15,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
        ),
        duration: isInfinite
            ? const Duration(days: 1)
            : Duration(seconds: message.length > 40 ? 5 : 2),
      ),
    );
}
// }

