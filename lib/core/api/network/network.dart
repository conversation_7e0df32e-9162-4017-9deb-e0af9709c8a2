import 'dart:async';
import 'dart:convert';
import 'dart:developer' as dev;

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:gp_h5/core/api/network/network_helper.dart' show NetworkHelper;
import 'package:gp_h5/core/api/network/network_logger.dart' show NetworkLogger;
import 'package:gp_h5/core/common_function.dart' show CommonFunctions;
import '../../../config/flavors/app_config.dart';
import '../../constants/enums.dart';

const enableLogs = true;

log(message, {name}) {
  if (enableLogs) dev.log(message, name: name ?? '');
}

class NetworkProvider {
  final Dio _dio;
  static final Map<String, Response> _cache = {};
  static bool _hasShownUnauthorizedError = false;

  NetworkProvider({String? baseUrl})
    : _dio = Dio(
        BaseOptions(
          baseUrl: baseUrl ?? AppConfig.instance.baseUrl,
          headers: {"Content-Type": "application/json"},
        ),
      ) {
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      RetryInterceptor(
        dio: _dio,
        logPrint: print,
        retries: 3,
        retryEvaluator: (error, attempt) => _retryEvaluator()(error, attempt),
      ),
    );

    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // NetworkLogger.logRequest(options);

          return handler.next(options);
        },
        onResponse: (response, handler) {
          NetworkLogger.logResponse(response);
          return handler.next(response);
        },
        onError: (error, handler) async {
          NetworkLogger.logError(error);

          if (error.requestOptions.extra.containsKey('retry')) {
            try {} catch (refreshError) {
              return handler.reject(
                DioException(
                  requestOptions: error.requestOptions,
                  error: 'Failed to refresh token',
                ),
              );
            }
          } else if (error.response?.statusCode == 401 &&
              (error.requestOptions.path != '/login')) {
            if (!_hasShownUnauthorizedError) {
              _hasShownUnauthorizedError = true;
            }
          }

          return handler.next(
            DioException(
              requestOptions: error.requestOptions,
              response: error.response,
              error:
                  CommonFunctions().errorMapping(error.response) ??
                  'Unknown error occurred',
            ),
          );
        },
      ),
    );
  }

  RetryEvaluator _retryEvaluator() => (error, attempt) {
    if (error.requestOptions.method != 'GET') return false;

    final statusCode = error.response?.statusCode;
    if (statusCode == 400 ||
        statusCode == 401 ||
        statusCode == 403 ||
        statusCode == 404) {
      return false;
    }

    return true;
  };

  Future<Response<T>> _makeRequest<T>(
    RequestType method,
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    final cacheKey = _generateCacheKey(
      method,
      path,
      data ?? queryParameters ?? {},
    );

    if (_cache.containsKey(cacheKey) && force && method == RequestType.get) {
      return _cache[cacheKey]! as Response<T>;
    }
    if (isSigninRequired) {
      options ??= Options();
      options.headers ??= {};
      options.headers!.addAll({'auth': true});
    }
    try {
      Response<T> response;
      switch (method) {
        case RequestType.get:
          response = await _dio.get<T>(
            path,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onReceiveProgress: onReceiveProgress,
          );
          break;
        case RequestType.post:
          response = await _dio.post<T>(
            path,
            data: CommonFunctions().removeNullValues(data ?? {}),
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onSendProgress: onSendProgress,
            onReceiveProgress: onReceiveProgress,
          );
          break;
        case RequestType.put:
          response = await _dio.put<T>(
            path,
            data: data,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onSendProgress: onSendProgress,
            onReceiveProgress: onReceiveProgress,
          );
          break;
        case RequestType.delete:
          response = await _dio.delete<T>(
            path,
            data: data,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
          );
          break;
        case RequestType.patch:
          response = await _dio.patch<T>(
            path,
            data: CommonFunctions().removeNullValues(data ?? {}),
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
          );
          break;
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        _cache[cacheKey] = response;
      }

      return response;
    } catch (error) {
      return Future.error(error);
    }
  }

  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onReceiveProgress,
    bool isSigninRequired = false,
    bool force = false,
  }) async {
    return _makeRequest<T>(
      RequestType.get,
      path,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
      force: force,
      isSigninRequired: isSigninRequired,
    );
  }

  Future<Response<T>> post<T>(
    String path, {
    Map<String, dynamic>? data,
    FormData? formData,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    return _makeRequest<T>(
      RequestType.post,
      path,
      data: data ?? formData,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      force: force,
      isSigninRequired: isSigninRequired,
    );
  }

  Future<Response<T>> put<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    return _makeRequest<T>(
      RequestType.put,
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      force: force,
      isSigninRequired: isSigninRequired,
    );
  }

  Future<Response<T>> delete<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    return _makeRequest<T>(
      RequestType.delete,
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      force: force,
      isSigninRequired: isSigninRequired,
    );
  }

  Future<Response<T>> patch<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    return _makeRequest<T>(
      RequestType.patch,
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      force: force,
      isSigninRequired: isSigninRequired,
    );
  }

  Future<Response<T>> formData<T>(
    String path, {
    FormData? formData,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    if (isSigninRequired) {
      options ??= Options();
      options.headers ??= {};
      options.headers!.addAll({'auth': true});
    }
    return _dio.post<T>(
      path,
      data: formData,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );
  }

  Future<Response<T>> retryRequest<T>(RequestOptions requestOptions) async {
    final Completer<Response<T>> responseCompleter = Completer<Response<T>>();

    responseCompleter.complete(request<T>(requestOptions));

    return responseCompleter.future;
  }

  Future<Response<T>> request<T>(RequestOptions requestOptions) async {
    return _dio.request<T>(
      requestOptions.path,
      cancelToken: requestOptions.cancelToken,
      data: requestOptions.data,
      onReceiveProgress: requestOptions.onReceiveProgress,
      onSendProgress: requestOptions.onSendProgress,
      queryParameters: requestOptions.queryParameters,
      options: Options(
        method: requestOptions.method,
        sendTimeout: requestOptions.sendTimeout,
        receiveTimeout: requestOptions.receiveTimeout,
        extra: requestOptions.extra,
        headers: requestOptions.headers,
        responseType: requestOptions.responseType,
        contentType: requestOptions.contentType,
        validateStatus: requestOptions.validateStatus,
        receiveDataWhenStatusError: requestOptions.receiveDataWhenStatusError,
        followRedirects: requestOptions.followRedirects,
        maxRedirects: requestOptions.maxRedirects,
        persistentConnection: requestOptions.persistentConnection,
        requestEncoder: requestOptions.requestEncoder,
        responseDecoder: requestOptions.responseDecoder,
        listFormat: requestOptions.listFormat,
      ),
    );
  }

  String _generateCacheKey(
    RequestType method,
    String url,
    Map<String, dynamic> data,
  ) {
    final methodString = method.name;
    final dataString = jsonEncode(data);
    return '$methodString|$url|$dataString';
  }

  static void clearCache() {
    _cache.clear();
  }
}
