
import 'app_config.dart';

void setupDYZBConfig() {
   AppConfig(
    appName: 'deying',
    siteId: '83',
    baseUrl: 'https://xxxxx.qeiqazdy.lknhvfdsal.com',
    marketWsUrl: 'wss://xxxxx.qeiqazdy.lknhvfdsal.com/ws',
    inviteLinkUrl: 'https://xxxxx.qeiqazdy.lknhvfdsal.com/#/?inviteCode=',
    electron: ElectronConfig(
      appId: 'com.deying.app',
      productName: '德盈资本',
      icon: IconConfig(
        window: 'brands/deying/assets/icon.png',
        tray: 'brands/deying/assets/tray.png',
        build: IconBuildConfig(
          mac: 'brands/deying/assets/icon.icns',
          win: 'brands/deying/assets/icon.ico',
          linux: 'brands/deying/assets/icon.png',
        ),
      ),
      window: WindowConfig(
        minWidth: 1400,
        minHeight: 1200,
        autoHideMenuBar: true,
      ),
      build: BuildConfig(
        directories: BuildDirectoriesConfig(
          output: 'release/\${version}',
        ),
        artifactName: ArtifactNameConfig(
          mac: '德盈资本-Mac-\${version}-Installer.\${ext}',
          win: '德盈资本-Windows-\${version}-Setup.\${ext}',
        ),
      ),
      macIdentity: 'Parekh Gujjar (V52VA2KKYX)',
    ),
  );
}
