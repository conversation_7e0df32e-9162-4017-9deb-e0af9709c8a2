// ignore_for_file: use_build_context_synchronously

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/scheduler.dart';
import 'package:gp_h5/core/api/network/models/bad_request.dart';

class CommonFunctions {
  static void afterInit(Function function) =>
      SchedulerBinding.instance.addPostFrameCallback((_) => function());

  static showDialogPopUp(
    BuildContext context,
    Widget dialogWidget, {
    bool barrierDismissible = true,
    String? routeName,
  }) =>
      showGeneralDialog(
        barrierDismissible: barrierDismissible,
        context: context,
        barrierLabel: "",
        routeSettings:
            routeName == null ? null : RouteSettings(name: routeName),
        pageBuilder: (ctx, a1, a2) => Container(),
        transitionBuilder: (ctx, a1, a2, child) {
          // var curve = Curves.easeInOut.transform(a1.value);
          return WillPopScope(
            onWillPop: () async {
              // if (barrierDismissible) Navigator.pop(context);
              return barrierDismissible;
            },
            child: dialogWidget,
            // child: Transform.scale(
            //   scale: curve,
            //   child: dialogWidget,
            // ),
          );
        },
        // transitionDuration: const Duration(milliseconds: 300),
      );




  dynamic errorMapping(Response? response) {
    final badRequest = <BadRequest>[]; // List to store BadRequest objects
    var errorString = ''; // String to accumulate error messages

    // Check if response and response.data are not null
    if (response?.data is Map) {
      // Check for specific error types
      if (response?.data.containsKey('msg')) {
        final message = response?.data['msg'];
        if (message is String) {
          badRequest.add(BadRequest(error: [message]));
        }
      } else if (response?.data.containsKey('error')) {
        final error = response?.data['error'];
        if (error is String) {
          badRequest.add(BadRequest(error: [error]));
        }
      }
    } else {
      // Handle case where response.data is null or not a Map
      badRequest.add(BadRequest(error: ['Invalid response format']));
    }

    // Construct error string from badRequest list
    for (var element in badRequest) {
      var subString = '';
      element.error?.forEach((sub) {
        subString = '$subString\n$sub';
      });
      if (errorString.isEmpty) {
        errorString = subString;
      } else {
        errorString = '$errorString\n\n$subString';
      }
    }

    return errorString;
  }

  String replaceCharacters(String text) =>
      capitalizeFirstLetter(text.replaceAll(RegExp('[\\W_]+'), ' '));

  String capitalizeFirstLetter(String input) {
    if (input.isEmpty) {
      return input; // Return an empty string if the input is empty.
    }
    return input[0].toUpperCase() + input.substring(1);
  }

  Map<String, dynamic> removeNullValues(Map<String, dynamic> input) =>
      Map.fromEntries(input.entries.where((e) => e.value != null));
}
