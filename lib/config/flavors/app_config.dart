
/// Defines the flavor of the app
enum Flavor {
  pre, // 预发布环境
  gp, // 测试环境
  rsyp, // 荣顺优配 (生产-演示站)
  yhxt, // 沅和信投 (生产-租户站)
  tempa, // 金盾 (生产-租户站)
  bszb, // 宝石资本 (生产-租户站)
  dyzb, // 德盈资本 (生产-租户站)
}

/// Configuration for the app based on flavor

// app_config.dart

class AppConfig {
  final String appName;
  final String siteId;
  final String baseUrl;
  final String marketWsUrl;
  final String inviteLinkUrl;
  final ElectronConfig electron;

  factory AppConfig({
    required this.appName,
    required this.siteId,
    required this.baseUrl,
    required this.marketWsUrl,
    required this.inviteLinkUrl,
    required this.electron,
  });

}

class ElectronConfig {
  final String appId;
  final String productName;
  final IconConfig icon;
  final WindowConfig window;
  final BuildConfig build;
  final String macIdentity;

  const ElectronConfig({
    required this.appId,
    required this.productName,
    required this.icon,
    required this.window,
    required this.build,
    required this.macIdentity,
  });
}

class IconConfig {
  final String window;
  final String? tray;
  final IconBuildConfig build;

  const IconConfig({
    required this.window,
    this.tray,
    required this.build,
  });
}

class IconBuildConfig {
  final String mac;
  final String win;
  final String linux;

  const IconBuildConfig({
    required this.mac,
    required this.win,
    required this.linux,
  });
}

class WindowConfig {
  final int minWidth;
  final int minHeight;
  final bool autoHideMenuBar;

  const WindowConfig({
    required this.minWidth,
    required this.minHeight,
    required this.autoHideMenuBar,
  });
}

class BuildConfig {
  final BuildDirectoriesConfig directories;
  final ArtifactNameConfig artifactName;

  const BuildConfig({
    required this.directories,
    required this.artifactName,
  });
}

class BuildDirectoriesConfig {
  final String output;

  const BuildDirectoriesConfig({
    required this.output,
  });
}

class ArtifactNameConfig {
  final String mac;
  final String win;

  const ArtifactNameConfig({
    required this.mac,
    required this.win,
  });
}
