import 'package:gp_h5/config/flavors/app_config.dart';

void setupBSZBConfig() {
  AppConfig(
    appName: 'gemstone',
    siteId: '82',
    baseUrl: 'https://pc.bs666.vip',
    marketWsUrl: 'wss://pc.bs666.vip/ws',
    inviteLinkUrl: 'https://pc.bs666.vip/#/?inviteCode=',
    electron: ElectronConfig(
      appId: 'com.gemstone.app',
      productName: '宝石资本',
      icon: IconConfig(
        window: 'brands/gemstone/assets/icon.png',
        tray: 'brands/gemstone/assets/tray.png',
        build: IconBuildConfig(
          mac: 'brands/gemstone/assets/icon.icns',
          win: 'brands/gemstone/assets/icon.ico',
          linux: 'brands/gemstone/assets/icon.png',
        ),
      ),
      window: WindowConfig(
        minWidth: 1400,
        minHeight: 1200,
        autoHideMenuBar: true,
      ),
      build: BuildConfig(
        directories: BuildDirectoriesConfig(
          output: 'release/\${version}',
        ),
        artifactName: ArtifactNameConfig(
          mac: '宝石资本-Mac-\${version}-Installer.\${ext}',
          win: '宝石资本-Windows-\${version}-Setup.\${ext}',
        ),
      ),
      macIdentity: 'Parekh Gujjar (V52VA2KKYX)',
    ),
  );
}
