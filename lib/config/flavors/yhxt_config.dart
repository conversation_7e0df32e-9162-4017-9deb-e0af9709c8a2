import 'package:gp_h5/config/flavors/app_config.dart';

void setupYHXTConfig() {
  AppConfig(
    appName: '沅和信投',
    siteId: '81',
    baseUrl: 'https://pc.490752.com',
    marketWsUrl: 'wss://pc.490752.com/ws',
    inviteLinkUrl: 'https://pc.490752.com/#/?inviteCode=',
    electron: ElectronConfig(
      appId: 'com.yhxt.stock',
      productName: '沅和信投',
      icon: IconConfig(
        window: 'assets/brands/yhxt/icon.png',
        tray: 'assets/brands/yhxt/tray.png',
        build: IconBuildConfig(
          mac: 'assets/brands/yhxt/icon.icns',
          win: 'assets/brands/yhxt/icon.ico',
          linux: 'assets/brands/yhxt/icon.png',
        ),
      ),
      window: WindowConfig(minWidth: 800, minHeight: 600, autoHideMenuBar: true),
      build: BuildConfig(
        directories: BuildDirectoriesConfig(output: 'build/yhxt'),
        artifactName: ArtifactNameConfig(mac: 'yhxt_mac', win: 'yhxt_win'),
      ),
      macIdentity: 'YHXT_IDENTITY',
    ),
  );
}
